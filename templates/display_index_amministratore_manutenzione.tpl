{include file="header_amministratore.tpl"}

<br>
<table width='100%' class="sfondo_base_generico contenitore_generico bordo_generico_rilievo" style="border-collapse: separate;">
	<tr>
    	<td>
<table width='100%'>
    <tr  class="div_titolo_scheda_generica sfondo_scuro" align='center'>
        <td valign='top' width='90%'>
            <table width='90%'>
                <tr>
                        <font color='#f7f8f9' size='5'>Manutenzione: selezionare l'opzione desiderata </font>
                </tr>
                {if $scuola_bloccata == 'SI' }
                    <tr>
                        <td align='center' colspan='7'>
                            <font color='#ff0000' size='5'><blink>Attenzione: scuola bloccata!!</blink></font>
                        </td>
                   </tr>
                {/if}
            </table>
</td>
</tr>
<tr>
    <td valign='top' width='100%'>
        <table width='90%' align='center' border="1">
            <form method='post' action='{$SCRIPT_NAME}'>
                <tr class="div_corpo_scheda_generica" style="height:50px">
                    <td align='center' colspan='6'>
                        <font color='#000000' size='4'>Attenzione! Pannello di manutenzione. Usare con prudenza!!</font>
                    </td>
                    <td align='center' colspan='1'>
                        {if $scuola_bloccata == 'SI' }
                            <input type='submit'  value='Apri scuola' class="btn_manutenzione">
                        {else}
                            <input type='submit'  value='Blocca scuola' class="btn_manutenzione">
                        {/if}
                    </td>
                    <td align='center' colspan='1'>
                        <input type='button' onclick='switch_classi();' value='Seleziona tutte le classi' class="btn_manutenzione">
                    </td>
                </tr>
                <input type='hidden' name='form_stato' value='{$form_stato}'>
                <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                <input type='hidden' name='stato_secondario' value='blocca'>
                <input type='hidden' name='current_user' value='{$current_user}'>
                <input type='hidden' name='current_key' value='{$current_key}'>
            </form>
            <form method='post' action='{$SCRIPT_NAME}' target='_blank'>
                <tr class="div_corpo_scheda_generica"  style="height:50px">
                    <td align='center' colspan='8'>
                        <input disabled type='button' id='button_inseritore' value='Inseritore orario fake' onclick="set_folder('inseritore');" class="btn_manutenzione">
                        {*<input type='button' id='button_importatore' value='Importatore orario vecchio' onclick="set_folder('importatore');">*}
                        <input type='button' id='button_sostitutore' value='Sostitutore fasce orarie' onclick="set_folder('sostitutore');" class="btn_manutenzione">
                        <input type='button' id='button_copiatore' value='Copiatore orario settimana' onclick="set_folder('copiatore');" class="btn_manutenzione">
                        <input type='button' id='button_misc' value='Operazioni varie' onclick="set_folder('misc');" class="btn_manutenzione">
                        <input type='button' id='button_report' value='Parametri e report' onclick="set_folder('report');" class="btn_manutenzione">
                    </td>
                </tr>
                <input type='hidden' id='hid_operazione' name='operazione' value='insert_fake'>

                <tr id="grid_classes">
                   {* <td align='center' colspan='8'>
                        {mastercom_grid_checkbox mat_dati=$elenco_classi tot_colonne=8 chiave_id=id_classe chiave_descrizione=descrizione_completa}
                    </td>*}
                    <td align='center' colspan='8'>
                        {mastercom_grid_classes
                            mat_classi=$elenco_classi_accessibili_generale
                            mat_checks=$mat_checks
                            onclick_submit='NO'
                            default_background='y'
                            onclick_set_hidden_id='id_classe'
                            onclick_set_hidden_name='classe'
                            status_light ='no'
                            checks_active='si'
                            ver2_label_bottone='label'
                            bold_text='y'
                            only_main='y'
                        }
                    </td>
                </tr>
                <tr id='row_start' class="div_corpo_scheda_generica" style="height:40px">
                    {*{{{ *}
                    <td align='center'>
                        <b>GG</b>
                    </td>
                    <td align='center'>
                        {mastercom_auto_select name="giorno"}
                        01###01
                        @@@02###02
                        @@@03###03
                        @@@04###04
                        @@@05###05
                        @@@06###06
                        @@@07###07
                        @@@08###08
                        @@@09###09
                        @@@10###10
                        @@@11###11
                        @@@12###12
                        @@@13###13
                        @@@14###14
                        @@@15###15
                        @@@16###16
                        @@@17###17
                        @@@18###18
                        @@@19###19
                        @@@20###20
                        @@@21###21
                        @@@22###22
                        @@@23###23
                        @@@24###24
                        @@@25###25
                        @@@26###26
                        @@@27###27
                        @@@28###28
                        @@@29###29
                        @@@30###30
                        @@@31###31
                        {/mastercom_auto_select}
                    </td>
                    <td align='center'>
                        <b>MM</b>
                    </td>
                    <td align='center'>
                        {mastercom_auto_select name="mese"}
                        01###01
                        @@@02###02
                        @@@03###03
                        @@@04###04
                        @@@05###05
                        @@@06###06
                        @@@07###07
                        @@@08###08
                        @@@09###09
                        @@@10###10
                        @@@11###11
                        @@@12###12
                        {/mastercom_auto_select}
                    </td>
                    <td align='center'>
                        <b>AA</b>
                    </td>
                    <td align='center'>
                        {mastercom_auto_select name="anno"}
                        2001###2001
                            {for $var=2002 to $anno_fine}
                                @@@{$var}###{$var}
                            {/for}
                        {/mastercom_auto_select}
                    </td>
                    <td align='center'>
                        <b>Ripeti per</b>
                    </td>
                    <td align='center'>
                        {mastercom_auto_select name="num_giorni"}
                        01###01
                        @@@02###02
                        @@@03###03
                        @@@04###04
                        @@@05###05
                        @@@06###06
                        @@@07###07
                        @@@08###08
                        @@@09###09
                        @@@10###10
                        @@@11###11
                        @@@12###12
                        @@@13###13
                        @@@14###14
                        @@@15###15
                        @@@16###16
                        @@@17###17
                        @@@18###18
                        @@@19###19
                        @@@20###20
                        @@@21###21
                        @@@22###22
                        @@@23###23
                        @@@24###24
                        @@@25###25
                        @@@26###26
                        @@@27###27
                        @@@28###28
                        @@@29###29
                        @@@30###30
                        @@@31###31
                        @@@32###32
                        @@@33###33
                        @@@34###34
                        @@@35###35
                        @@@36###36
                        @@@37###37
                        @@@38###38
                        @@@39###39
                        @@@40###40
                        @@@41###41
                        @@@42###42
                        @@@43###43
                        @@@44###44
                        @@@45###45
                        @@@46###46
                        @@@47###47
                        @@@48###48
                        @@@49###49
                        @@@50###50
                        @@@51###51
                        @@@52###52
                        @@@53###53
                        @@@54###54
                        @@@55###55
                        @@@56###56
                        @@@57###57
                        @@@58###58
                        @@@59###59
                        @@@60###60
                        @@@61###61
                        @@@62###62
                        @@@63###63
                        @@@64###64
                        @@@65###65
                        @@@66###66
                        @@@67###67
                        @@@68###68
                        @@@69###69
                        @@@70###70
                        @@@71###71
                        @@@72###72
                        @@@73###73
                        @@@74###74
                        @@@75###75
                        @@@76###76
                        @@@77###77
                        @@@78###78
                        @@@79###79
                        @@@80###80
                        @@@81###81
                        @@@82###82
                        @@@83###83
                        @@@84###84
                        @@@85###85
                        @@@86###86
                        @@@87###87
                        @@@88###88
                        @@@89###89
                        @@@90###90
                        @@@91###91
                        @@@92###92
                        @@@93###93
                        @@@94###94
                        @@@95###95
                        @@@96###96
                        @@@97###97
                        @@@98###98
                        @@@99###99
                        @@@100###100
                        @@@101###101
                        @@@102###102
                        @@@103###103
                        @@@104###104
                        @@@105###105
                        @@@106###106
                        @@@107###107
                        @@@108###108
                        @@@109###109
                        @@@110###110
                        @@@111###111
                        @@@112###112
                        @@@113###113
                        @@@114###114
                        @@@115###115
                        @@@116###116
                        @@@117###117
                        @@@118###118
                        @@@119###119
                        @@@120###120
                        @@@121###121
                        @@@122###122
                        @@@123###123
                        @@@124###124
                        @@@125###125
                        @@@126###126
                        @@@127###127
                        @@@128###128
                        @@@129###129
                        @@@130###130
                        @@@131###131
                        @@@132###132
                        @@@133###133
                        @@@134###134
                        @@@135###135
                        @@@136###136
                        @@@137###137
                        @@@138###138
                        @@@139###139
                        @@@140###140
                        @@@141###141
                        @@@142###142
                        @@@143###143
                        @@@144###144
                        @@@145###145
                        @@@146###146
                        @@@147###147
                        @@@148###148
                        @@@149###149
                        @@@150###150
                        @@@151###151
                        @@@152###152
                        @@@153###153
                        @@@154###154
                        @@@155###155
                        @@@156###156
                        @@@157###157
                        @@@158###158
                        @@@159###159
                        @@@160###160
                        @@@161###161
                        @@@162###162
                        @@@163###163
                        @@@164###164
                        @@@165###165
                        @@@166###166
                        @@@167###167
                        @@@168###168
                        @@@169###169
                        @@@170###170
                        @@@171###171
                        @@@172###172
                        @@@173###173
                        @@@174###174
                        @@@175###175
                        @@@176###176
                        @@@177###177
                        @@@178###178
                        @@@179###179
                        @@@180###180
                        @@@181###181
                        @@@182###182
                        @@@183###183
                        @@@184###184
                        @@@185###185
                        @@@186###186
                        @@@187###187
                        @@@188###188
                        @@@189###189
                        @@@190###190
                        @@@191###191
                        @@@192###192
                        @@@193###193
                        @@@194###194
                        @@@195###195
                        @@@196###196
                        @@@197###197
                        @@@198###198
                        @@@199###199
                        @@@200###200
                        @@@201###201
                        @@@202###202
                        @@@203###203
                        @@@204###204
                        @@@205###205
                        @@@206###206
                        @@@207###207
                        @@@208###208
                        @@@209###209
                        @@@210###210
                        @@@211###211
                        @@@212###212
                        @@@213###213
                        @@@214###214
                        @@@215###215
                        @@@216###216
                        @@@217###217
                        @@@218###218
                        @@@219###219
                        @@@220###220
                        @@@221###221
                        @@@222###222
                        @@@223###223
                        @@@224###224
                        @@@225###225
                        @@@226###226
                        @@@227###227
                        @@@228###228
                        @@@229###229
                        @@@230###230
                        @@@231###231
                        @@@232###232
                        @@@233###233
                        @@@234###234
                        @@@235###235
                        @@@236###236
                        @@@237###237
                        @@@238###238
                        @@@239###239
                        @@@240###240
                        @@@241###241
                        @@@242###242
                        @@@243###243
                        @@@244###244
                        @@@245###245
                        @@@246###246
                        @@@247###247
                        @@@248###248
                        @@@249###249
                        @@@250###250
                        @@@251###251
                        @@@252###252
                        @@@253###253
                        @@@254###254
                        @@@255###255
                        @@@256###256
                        @@@257###257
                        @@@258###258
                        @@@259###259
                        @@@260###260
                        @@@261###261
                        @@@262###262
                        @@@263###263
                        @@@264###264
                        @@@265###265
                        @@@266###266
                        @@@267###267
                        @@@268###268
                        @@@269###269
                        @@@270###270
                        @@@271###271
                        @@@272###272
                        @@@273###273
                        @@@274###274
                        @@@275###275
                        @@@276###276
                        @@@277###277
                        @@@278###278
                        @@@279###279
                        @@@280###280
                        @@@281###281
                        @@@282###282
                        @@@283###283
                        @@@284###284
                        @@@285###285
                        @@@286###286
                        @@@287###287
                        @@@288###288
                        @@@289###289
                        @@@290###290
                        @@@291###291
                        @@@292###292
                        @@@293###293
                        @@@294###294
                        @@@295###295
                        @@@296###296
                        @@@297###297
                        @@@298###298
                        @@@299###299
                        @@@300###300
                        @@@301###301
                        @@@302###302
                        @@@303###303
                        @@@304###304
                        @@@305###305
                        @@@306###306
                        @@@307###307
                        @@@308###308
                        @@@309###309
                        @@@310###310
                        @@@311###311
                        @@@312###312
                        @@@313###313
                        @@@314###314
                        @@@315###315
                        @@@316###316
                        @@@317###317
                        @@@318###318
                        @@@319###319
                        @@@320###320
                        @@@321###321
                        @@@322###322
                        @@@323###323
                        @@@324###324
                        @@@325###325
                        @@@326###326
                        @@@327###327
                        @@@328###328
                        @@@329###329
                        @@@330###330
                        @@@331###331
                        @@@332###332
                        @@@333###333
                        @@@334###334
                        @@@335###335
                        @@@336###336
                        @@@337###337
                        @@@338###338
                        @@@339###339
                        @@@340###340
                        @@@341###341
                        @@@342###342
                        @@@343###343
                        @@@344###344
                        @@@345###345
                        @@@346###346
                        @@@347###347
                        @@@348###348
                        @@@349###349
                        @@@350###350
                        @@@351###351
                        @@@352###352
                        @@@353###353
                        @@@354###354
                        @@@355###355
                        @@@356###356
                        @@@357###357
                        @@@358###358
                        @@@359###359
                        @@@360###360
                        @@@361###361
                        @@@362###362
                        @@@363###363
                        @@@364###364
                        @@@365###365
                        {/mastercom_auto_select}
                    </td>
                    {*}}}*}
                </tr>
                <tr id='row_weeks' style='display: none;' class="div_corpo_scheda_generica" style="height:40px">
                    {*{{{ *}
                    <td colspan='8'>
                        <table width='100%'>
                            <tr>
                                <td align='center' colspan='12'>Selezionare la settimana desiderata<input type='button' value='{mastercom_label}Seleziona tutte{/mastercom_label}' onclick='grid_selector("mat_checkbox[]");'></td>
                            </tr>
                            <tr>
                                <td align='center' colspan='12'><input type="radio" name="selected_week" value="0">Template</td>
                            </tr>
                            <tr>
                                <td><b>Orig</b></td>
                                <td><b>Dest</b></td>
                                <td><b>Settimana</b></td>
                                <td><b>Orig</b></td>
                                <td><b>Dest</b></td>
                                <td><b>Settimana</b></td>
                                <td><b>Orig</b></td>
                                <td><b>Dest</b></td>
                                <td><b>Settimana</b></td>
                                <td><b>Orig</b></td>
                                <td><b>Dest</b></td>
                                <td><b>Settimana</b></td>
                            </tr>
                            {section name=cont_ext loop=$start}
                                <tr class="div_corpo_scheda_generica" style="height:40px">
                                    {section name=cont loop=$elenco_settimane start=$start[cont_ext] max=4 step=$step}
                                        <td>
                                            <input type="radio" name="selected_week" value="{$elenco_settimane[cont].valore}">
                                        </td>
                                        <td>
                                            <input type="checkbox" name="mat_checkbox[]" value="{$elenco_settimane[cont].valore}">
                                        </td>
                                        <td>
                                            {$elenco_settimane[cont].nome}
                                        </td>
                                    {/section}
                                </tr>
                            {/section}
                        </table>

                    </td>
                    {*}}}*}
                </tr>
                <tr id='row_days' class="div_corpo_scheda_generica" style="height:40px">
                    {*{{{ *}
                    <td align='center'>
                        <b>Giorni</b>
                    </td>
                    <td align='center' colspan=7>
                        <input checked type='checkbox' name='mat_sett[]' value=1>L&nbsp;
                        <input checked type='checkbox' name='mat_sett[]' value=2>M&nbsp;
                        <input checked type='checkbox' name='mat_sett[]' value=3>M&nbsp;
                        <input checked type='checkbox' name='mat_sett[]' value=4>G&nbsp;
                        <input checked type='checkbox' name='mat_sett[]' value=5>V&nbsp;
                        <input checked type='checkbox' name='mat_sett[]' value=6>S&nbsp;
                        <input type='checkbox' name='mat_sett[]' value=0>D&nbsp;
                    </td>
                    {*}}}*}
                </tr>
                <tr id='row_ore' class="div_corpo_scheda_generica" style="height:40px">
                    {*{{{ *}
                    <td align='center'>
                        <b>Ora inizio</b>
                    </td>
                    <td align='center'>
                        {mastercom_auto_select name='ora_inizio'}
                        00###00
                        @@@01###01
                        @@@02###02
                        @@@03###03
                        @@@04###04
                        @@@05###05
                        @@@06###06
                        @@@07###07
                        @@@08###08
                        @@@09###09
                        @@@10###10
                        @@@11###11
                        @@@12###12
                        @@@13###13
                        @@@14###14
                        @@@15###15
                        @@@16###16
                        @@@17###17
                        @@@18###18
                        @@@19###19
                        @@@20###20
                        @@@21###21
                        @@@22###22
                        @@@23###23
                        {/mastercom_auto_select}
                    </td>
                    <td align='center'>
                        <b>Min inizio</b>
                    </td>
                    <td align='center'>
                        {mastercom_auto_select name="min_inizio"}
                        00###00
                        @@@01###01
                        @@@02###02
                        @@@03###03
                        @@@04###04
                        @@@05###05
                        @@@06###06
                        @@@07###07
                        @@@08###08
                        @@@09###09
                        @@@10###10
                        @@@11###11
                        @@@12###12
                        @@@13###13
                        @@@14###14
                        @@@15###15
                        @@@16###16
                        @@@17###17
                        @@@18###18
                        @@@19###19
                        @@@20###20
                        @@@21###21
                        @@@22###22
                        @@@23###23
                        @@@24###24
                        @@@25###25
                        @@@26###26
                        @@@27###27
                        @@@28###28
                        @@@29###29
                        @@@30###30
                        @@@31###31
                        @@@32###32
                        @@@33###33
                        @@@34###34
                        @@@35###35
                        @@@36###36
                        @@@37###37
                        @@@38###38
                        @@@39###39
                        @@@40###40
                        @@@41###41
                        @@@42###42
                        @@@43###43
                        @@@44###44
                        @@@45###45
                        @@@46###46
                        @@@47###47
                        @@@48###48
                        @@@49###49
                        @@@50###50
                        @@@51###51
                        @@@52###52
                        @@@53###53
                        @@@54###54
                        @@@55###55
                        @@@56###56
                        @@@57###57
                        @@@58###58
                        @@@59###59
                        {/mastercom_auto_select}
                    </td>
                    <td align='center'>
                        <b>Ora fine</b>
                    </td>
                    <td align='center'>
                        {mastercom_auto_select name='ora_fine'}
                        00###00
                        @@@01###01
                        @@@02###02
                        @@@03###03
                        @@@04###04
                        @@@05###05
                        @@@06###06
                        @@@07###07
                        @@@08###08
                        @@@09###09
                        @@@10###10
                        @@@11###11
                        @@@12###12
                        @@@13###13
                        @@@14###14
                        @@@15###15
                        @@@16###16
                        @@@17###17
                        @@@18###18
                        @@@19###19
                        @@@20###20
                        @@@21###21
                        @@@22###22
                        @@@23###23
                        {/mastercom_auto_select}
                    </td>
                    <td align='center'>
                        <b>Min fine</b>
                    </td>
                    <td align='center'>
                        {mastercom_auto_select name="min_fine"}
                        00###00
                        @@@01###01
                        @@@02###02
                        @@@03###03
                        @@@04###04
                        @@@05###05
                        @@@06###06
                        @@@07###07
                        @@@08###08
                        @@@09###09
                        @@@10###10
                        @@@11###11
                        @@@12###12
                        @@@13###13
                        @@@14###14
                        @@@15###15
                        @@@16###16
                        @@@17###17
                        @@@18###18
                        @@@19###19
                        @@@20###20
                        @@@21###21
                        @@@22###22
                        @@@23###23
                        @@@24###24
                        @@@25###25
                        @@@26###26
                        @@@27###27
                        @@@28###28
                        @@@29###29
                        @@@30###30
                        @@@31###31
                        @@@32###32
                        @@@33###33
                        @@@34###34
                        @@@35###35
                        @@@36###36
                        @@@37###37
                        @@@38###38
                        @@@39###39
                        @@@40###40
                        @@@41###41
                        @@@42###42
                        @@@43###43
                        @@@44###44
                        @@@45###45
                        @@@46###46
                        @@@47###47
                        @@@48###48
                        @@@49###49
                        @@@50###50
                        @@@51###51
                        @@@52###52
                        @@@53###53
                        @@@54###54
                        @@@55###55
                        @@@56###56
                        @@@57###57
                        @@@58###58
                        @@@59###59
                        {/mastercom_auto_select}
                    </td>
                    {*}}}*}
                </tr>
                <tr id='row_ore_dest' style='display: none;'>
                    {*{{{ *}
                    <td align='center'>
                        <b>Ora inizio dest.</b>
                    </td>
                    <td align='center'>
                        {mastercom_auto_select name='ora_inizio_dest'}
                        00###00
                        @@@01###01
                        @@@02###02
                        @@@03###03
                        @@@04###04
                        @@@05###05
                        @@@06###06
                        @@@07###07
                        @@@08###08
                        @@@09###09
                        @@@10###10
                        @@@11###11
                        @@@12###12
                        @@@13###13
                        @@@14###14
                        @@@15###15
                        @@@16###16
                        @@@17###17
                        @@@18###18
                        @@@19###19
                        @@@20###20
                        @@@21###21
                        @@@22###22
                        @@@23###23
                        {/mastercom_auto_select}
                    </td>
                    <td align='center'>
                        <b>Min inizio dest</b>
                    </td>
                    <td align='center'>
                        {mastercom_auto_select name="min_inizio_dest"}
                        00###00
                        @@@01###01
                        @@@02###02
                        @@@03###03
                        @@@04###04
                        @@@05###05
                        @@@06###06
                        @@@07###07
                        @@@08###08
                        @@@09###09
                        @@@10###10
                        @@@11###11
                        @@@12###12
                        @@@13###13
                        @@@14###14
                        @@@15###15
                        @@@16###16
                        @@@17###17
                        @@@18###18
                        @@@19###19
                        @@@20###20
                        @@@21###21
                        @@@22###22
                        @@@23###23
                        @@@24###24
                        @@@25###25
                        @@@26###26
                        @@@27###27
                        @@@28###28
                        @@@29###29
                        @@@30###30
                        @@@31###31
                        @@@32###32
                        @@@33###33
                        @@@34###34
                        @@@35###35
                        @@@36###36
                        @@@37###37
                        @@@38###38
                        @@@39###39
                        @@@40###40
                        @@@41###41
                        @@@42###42
                        @@@43###43
                        @@@44###44
                        @@@45###45
                        @@@46###46
                        @@@47###47
                        @@@48###48
                        @@@49###49
                        @@@50###50
                        @@@51###51
                        @@@52###52
                        @@@53###53
                        @@@54###54
                        @@@55###55
                        @@@56###56
                        @@@57###57
                        @@@58###58
                        @@@59###59
                        {/mastercom_auto_select}
                    </td>
                    <td align='center'>
                        <b>Ora fine dest</b>
                    </td>
                    <td align='center'>
                        {mastercom_auto_select name='ora_fine_dest'}
                        00###00
                        @@@01###01
                        @@@02###02
                        @@@03###03
                        @@@04###04
                        @@@05###05
                        @@@06###06
                        @@@07###07
                        @@@08###08
                        @@@09###09
                        @@@10###10
                        @@@11###11
                        @@@12###12
                        @@@13###13
                        @@@14###14
                        @@@15###15
                        @@@16###16
                        @@@17###17
                        @@@18###18
                        @@@19###19
                        @@@20###20
                        @@@21###21
                        @@@22###22
                        @@@23###23
                        {/mastercom_auto_select}
                    </td>
                    <td align='center'>
                        <b>Min fine dest</b>
                    </td>
                    <td align='center'>
                        {mastercom_auto_select name="min_fine_dest"}
                        00###00
                        @@@01###01
                        @@@02###02
                        @@@03###03
                        @@@04###04
                        @@@05###05
                        @@@06###06
                        @@@07###07
                        @@@08###08
                        @@@09###09
                        @@@10###10
                        @@@11###11
                        @@@12###12
                        @@@13###13
                        @@@14###14
                        @@@15###15
                        @@@16###16
                        @@@17###17
                        @@@18###18
                        @@@19###19
                        @@@20###20
                        @@@21###21
                        @@@22###22
                        @@@23###23
                        @@@24###24
                        @@@25###25
                        @@@26###26
                        @@@27###27
                        @@@28###28
                        @@@29###29
                        @@@30###30
                        @@@31###31
                        @@@32###32
                        @@@33###33
                        @@@34###34
                        @@@35###35
                        @@@36###36
                        @@@37###37
                        @@@38###38
                        @@@39###39
                        @@@40###40
                        @@@41###41
                        @@@42###42
                        @@@43###43
                        @@@44###44
                        @@@45###45
                        @@@46###46
                        @@@47###47
                        @@@48###48
                        @@@49###49
                        @@@50###50
                        @@@51###51
                        @@@52###52
                        @@@53###53
                        @@@54###54
                        @@@55###55
                        @@@56###56
                        @@@57###57
                        @@@58###58
                        @@@59###59
                        {/mastercom_auto_select}
                    </td>
                    {*}}}*}
                </tr>
                <tr id='row_insert' class="div_corpo_scheda_generica" style="height:40px">
                    {*{{{ *}
                    <td align='center'>
                        <b>Cancella giorno</b>
                    </td>
                    <td align='center'>
                        {mastercom_auto_select name='delete_day'}
                        NO###NO
                        @@@SI###SI
                        {/mastercom_auto_select}
                    </td>
                    <td align='center'>
                        <b>Cancella sovrapposizione</b>
                    </td>
                    <td align='center'>
                        {mastercom_auto_select name='delete_overlap'}
                        NO###NO
                        @@@SI###SI
                        {/mastercom_auto_select}
                    </td>
                    <td align='center'>
                        <b>Prof</b>
                    </td>
                    <td align='center'>
                        {mastercom_auto_select name='professore' array_dati=$elenco_professori}
                        -1###Professore non definito@@@
                        {/mastercom_auto_select}
                    </td>
                    <td align='center'>
                        <b>Mat</b>
                    </td>
                    <td align='center'>
                        {mastercom_auto_select name='materia' array_dati=$elenco_materie}
                        -1###Materia non definita@@@
                        {/mastercom_auto_select}
                    </td>
                    {*}}}*}
                </tr>
                <tr id='row_prof_mat_dest' style='display: none;' class="div_corpo_scheda_generica" style="height:40px">
                    {*{{{ *}
                    <td align='center'>
                        <b>Prof</b>
                    </td>
                    <td align='center'>
                        {mastercom_auto_select name='professore_orig' array_dati=$elenco_professori}
                        -1###Professore non definito@@@
                        {/mastercom_auto_select}
                    </td>
                    <td align='center'>
                        <b>Mat</b>
                    </td>
                    <td align='center'>
                        {mastercom_auto_select name='materia_orig' array_dati=$elenco_materie}
                        {/mastercom_auto_select}
                    </td>
                    <td align='center'>
                        <b>Prof dest</b>
                    </td>
                    <td align='center'>
                        {mastercom_auto_select name='professore_dest' array_dati=$elenco_professori}
                        -1###Professore non definito@@@
                        {/mastercom_auto_select}
                    </td>
                    <td align='center'>
                        <b>Mat dest</b>
                    </td>
                    <td align='center'>
                        {mastercom_auto_select name='materia_dest' array_dati=$elenco_materie}
                        {/mastercom_auto_select}
                    </td>
                    {*}}}*}
                </tr>
                <tr id='row_operations' style='display: none;' class="div_corpo_scheda_generica" style="height:40px">
                    {*{{{ *}
                    <td align='center' >
                        <b>Operazione da effettuare</b>
                    </td>
                    <td align='center' colspan='3'>
                        {mastercom_auto_select name='misc_operation'}
                        ---###---Scegliere operazione---
                        @@@CANCELLA_ASSENZE###Cancella assenze
                        @@@GIUSTIFICA_ASSENZE###Giustifica assenze
                        @@@CANCELLA_ASSENZE_GIORNALIERE###Cancella assenze giornaliere
                        @@@GIUSTIFICA_ASSENZE_GIORNALIERE###Giustifica assenze giornaliere
                        @@@CANCELLA_RITARDI_MATTINO###Cancella ritardi mattino
                        @@@GIUSTIFICA_RITARDI_MATTINO###Giustifica ritardi mattino
                        @@@CANCELLA_USCITE_MATTINO###Cancella uscite mattino
                        @@@GIUSTIFICA_USCITE_MATTINO###Giustifica uscite mattino
                        @@@CANCELLA_ASSENZE_MATTINO###Cancella assenze mattino
                        @@@GIUSTIFICA_ASSENZE_MATTINO###Giustifica assenze mattino
                        @@@CANCELLA_ASSENZE_POMERIGGIO###Cancella assenze pomeriggio
                        @@@GIUSTIFICA_ASSENZE_POMERIGGIO###Giustifica assenze pomeriggio
                        @@@CANCELLA_RITARDI_POMERIGGIO###Cancella ritardi pomeriggio
                        @@@GIUSTIFICA_RITARDI_POMERIGGIO###Giustifica ritardi pomeriggio
                        @@@CANCELLA_USCITE_POMERIGGIO###Cancella uscite pomeriggio
                        @@@GIUSTIFICA_USCITE_POMERIGGIO###Giustifica uscite pomeriggio
                        @@@CANCELLA_RITARDI_MINIMI###Cancella ritardi minimi
                        @@@GIUSTIFICA_RITARDI_MINIMI###Giustifica ritardi minimi
                        @@@CANCELLA_PERMESSI###Cancella tutti i permessi delle classi selezionate
                        @@@CANCELLA_VOTI###Cancella voti normali
                        @@@MUOVI_VOTI###Sposta dati da una materia di origine a una materia di destinazione per le classi selezionate e per il professore scelto
                        @@@MUOVI_VOTI_CLASSE###Sposta dati DI TUTTI I PROFESSORI da una materia di origine a una materia di destinazione per le classi selezionate
                        @@@MUOVI_VOTI_PROFESSORE###Sposta dati da un professore di origine a un professore di destinazione per le classi selezionate e per la materia scelta
                        @@@CANCELLA_VOTI_PAGELLINA###Cancella voti pagellina
                        @@@MUOVI_VOTI_PAGELLINA###Cambia materia voti pagellina
                        @@@APRI_SCRUTINIO###Apri tutti gli scrutini
                        @@@CHIUDI_SCRUTINIO###Chiudi tutti gli scrutini
                        @@@COPIA_PROPOSTE###Copia proposte voti pagellina su voti vuoti
                        @@@SBLOCCA_PAGELLINA###Sblocca voti pagellina modificati da amministratore
                        @@@ELIMINA_PAGELLINE_MULTIPLE###Elimina le pagelline multiple inserite e corregge i campi liberi
                        @@@CANCELLA_STUDENTI_DOPPI_NON_ABBINATI###Cancella gli studenti non abbinati che abbiano un omonimo gia inserito
                        @@@GENERA_CALENDARIO_FESTIVITA###Genera il calendario delle festivita per l'anno corrente
                        @@@GENERA_NUOVO_CURRICULUM_STORICO###Tenta l'adeguamento al nuovo stile di curriculum
                        @@@CORREGGI_NUOVO_CURRICULUM_STORICO###Corregge errori nel nuovo stile di curriculum
                        @@@ELIMINA_RIGHE_MULTIPLE_CURRICULUM###Elimina righe multiple di esiti di fine anno (Ammesso, Non ammesso e Giudizio Sospeso)
                        @@@UNIFORMA_TASSE###Uniforma tutte le tasse (prende quelle di tutti gli anni, le uniforma e le mette nel db attuale)
                        @@@RIPETI_CONTROLLO_ASSENZE###Esegue una nuova rilevazione presenze nelle classi e nei giorni selezionati, cancellando le vecchie asenze
                        @@@ADEGUA_ORARIO_VECCHIO_A_NUOVO###Adegua il vecchio orario scolastico basato sull'orario studenti al nuovo template di classe piu eccezioni
                        @@@CONTROLLA_STATO_ORARIO###Controlla lo stato dell'orario presente per ogni classe selezionata
                        @@@ESPORTA_STUDENTI_GESTWEB###Esporta verso GestWeb la lista degli studenti delle classi selezionate (N.B. attenzione agli studenti doppi per le classi multiple)
                        @@@AGGIORNA_STUDENTI_GESTWEB###Aggiorna verso GestWeb la lista degli studenti delle classi selezionate (N.B. attenzione agli studenti doppi per le classi multiple)
                        @@@ELIMINA_STUDENTI_GESTWEB###Elimina da GestWeb la lista degli studenti delle classi selezionate (N.B. attenzione agli studenti doppi per le classi multiple)
                        @@@ESITO_CORRENTE_CALCOLATO###Imposta Esito Corrente Calcolato per l'inizio dell'anno (In corso)
                        @@@SVUOTA_TABELLONE###Svuota tutti i dati dei tabelloni per le classi e periodo selezionati
                        {/mastercom_auto_select}
                    </td>
                    <td align='center' >&nbsp;
                        <b>Pagelline</b>
                    </td>
                    <td align='center' colspan='3'>&nbsp;
                        {mastercom_auto_select name="misc_period"}
                        1###1a pagellina infraquadrimestrale@@@
                        2###2a pagellina infraquadrimestrale@@@
                        3###3a pagellina infraquadrimestrale@@@
                        4###4a pagellina infraquadrimestrale@@@
                        5###5a pagellina infraquadrimestrale@@@
                        6###6a pagellina infraquadrimestrale@@@
                        7###Pagella fine 1o quadrimestre/trimestre@@@
                        8###Pagella fine 2o trimestre@@@
                        9###Pagella fine anno@@@
                        10###Valutazioni prove strutturate@@@
                        11###Prove d'esame di licenza
                        21###1a pagellina infraquadrimestrale Medie@@@
                        22###2a pagellina infraquadrimestrale Medie@@@
                        23###3a pagellina infraquadrimestrale Medie@@@
                        24###4a pagellina infraquadrimestrale Medie@@@
                        25###5a pagellina infraquadrimestrale Medie@@@
                        26###6a pagellina infraquadrimestrale Medie@@@
                        27###Pagella fine 1o quadrimestre/trimestre Medie@@@
                        28###Pagella fine 2o trimestre Medie@@@
                        29###Pagella fine anno Medie
                        {/mastercom_auto_select}
                    </td>
                    {*}}}*}
                </tr>
                <tr id='row_confirm' class="div_corpo_scheda_generica" style="height:40px">
                    <td align='center' colspan='8'>
                        <input type='image' name='bottone' value='Conferma' src='icone/icona.php?icon=ok&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma'>
                        <input type='hidden' name='form_stato' value='{$form_stato}'>
                        <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                        <input type='hidden' name='stato_secondario' value='update'>
                        <input type='hidden' name='current_user' value='{$current_user}'>
                        <input type='hidden' name='current_key' value='{$current_key}'>
                    </td>
                </tr>
            </form>
            <tr id='row_parameters' style='display: none;' class="div_corpo_scheda_generica" style="height:40px">
            <form method='post' action='{$SCRIPT_NAME}' target='_blank'>
                <td align='center' colspan='3'>
                    <b>Parametro</b><br>
                    {mastercom_auto_select name='form_nome_parametro' array_dati=$elenco_parametri}
                    -1###Parametro non definito@@@
                    {/mastercom_auto_select}
                </td>
                <td align='center' colspan='4'>
                    <b>Valore</b><br>
                    <input type='text' name='form_valore_parametro'>
                </td>
                <td align='center' colspan='1'>
                    <input type='image' name='bottone' value='Conferma' src='icone/icona.php?icon=ok&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma'>
                    <input type='hidden' name='form_stato' value='{$form_stato}'>
                    <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                    <input type='hidden' name='stato_secondario' value='setta_parametro'>
                    <input type='hidden' name='current_user' value='{$current_user}'>
                    <input type='hidden' name='current_key' value='{$current_key}'>
                </td>
            </form>

            </tr>
            <tr id='row_logo_upload' style='display: none;' class="div_corpo_scheda_generica" style="height:40px">
            <form  enctype="multipart/form-data" method='post' action='{$SCRIPT_NAME}' target='_blank'>
                <td align='center' colspan='7'>
                    <b>Logo</b><br>
                    {mastercom_smart_file
                                nome_pulsante="abilitazione_login"
                                contesto="istituto"
                                nome_file="logo"
                                dimensione="70"
                                dimensione_file="500000"
                                utente_corrente=$current_user
                    }
                </td>
                <td align='center' colspan='1'>
                    <input type='image' name='bottone' value='Conferma' src='icone/icona.php?icon=ok&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma'>
                    <input type='hidden' name='form_stato' value='{$form_stato}'>
                    <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                    <input type='hidden' name='stato_secondario' value='upload_logo'>
                    <input type='hidden' name='current_user' value='{$current_user}'>
                    <input type='hidden' name='current_key' value='{$current_key}'>
                </td>
            </form>

            </tr>
            <tr id='row_undelete' style='display: none;' class="div_corpo_scheda_generica" style="height:40px">
            <form method='post' action='{$SCRIPT_NAME}' target='_blank'>
                <td align='center' colspan='3'>
                    <b>Tipo dato da de-cancellare</b><br>
                    {mastercom_auto_select name='tipo_dato'}
                    -1###---@@@
                    indirizzo###Indirizzo@@@
                    classe###Classe@@@
                    studente###Studente@@@
                    utente###Utente@@@
                    materia###Materia
                    {/mastercom_auto_select}
                </td>
                <td align='center' colspan='4'>
                    <b>ID record da de-cancellare</b><br>
                    <input type='text' name='id_oggetto'>
                </td>
                <td align='center' colspan='1'>
                    <input type='image' name='bottone' value='Conferma' src='icone/icona.php?icon=ok&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma'>
                    <input type='hidden' name='form_stato' value='{$form_stato}'>
                    <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                    <input type='hidden' name='stato_secondario' value='decancella_dato'>
                    <input type='hidden' name='current_user' value='{$current_user}'>
                    <input type='hidden' name='current_key' value='{$current_key}'>
                </td>
            </form>

            </tr>
            <tr id='row_absence_check' style='display: none;' class="div_corpo_scheda_generica" style="height:40px">
            <form method='post' action='{$SCRIPT_NAME}' target='_blank'>
                <td align='center' colspan='3'>
                    Report controllo assenze<br>
                    {mastercom_auto_select name='tipo_dato'}
                    generale###Generale@@@
                    indirizzo###Indirizzo@@@
                    classe###Classe@@@
                    studente###Studente
                    {/mastercom_auto_select}
                </td>
                <td align='center' colspan='2'>
                    <b>Data di riferimento</b><br>
                    <input type='text' name='data'>
                </td>
                <td align='center' colspan='2'>
                    <b>ID record da controllare</b><br>
                    <input type='text' name='id_oggetto'>
                </td>
                <td align='center' colspan='1'>
                    <input type='image' name='bottone' value='Conferma' src='icone/icona.php?icon=ok&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma'>
                    <input type='hidden' name='form_stato' value='{$form_stato}'>
                    <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                    <input type='hidden' name='stato_secondario' value='report_sistema_assenze'>
                    <input type='hidden' name='current_user' value='{$current_user}'>
                    <input type='hidden' name='current_key' value='{$current_key}'>
                </td>
            </form>
            </tr>
            <tr id='row_reports_h' style='display: none;' class="div_corpo_scheda_generica" style="height:40px">
                <td align='center' colspan='1'>
                    <b>Parametri</b>
                </td>
                <td align='center' colspan='1'>
                    <b>Dati cancellati</b>
                </td>
                <td align='center' colspan='1'>
                    <b>Traduzioni</b>
                </td>
                <td align='center' colspan='5'>
                    <b>Report vari</b>
                </td>
            </tr>
            <tr id='row_reports' style='display: none;' class="div_corpo_scheda_generica" style="height:40px">
            <form method='post' action='{$SCRIPT_NAME}' target='_blank'>
                <td align='center' colspan='1'
                    style='display: flex;
                        align-items: center;
                        justify-content: space-evenly;'
                >
                    <input type='image' name='bottone' value='Stampa' src='icone/icona.php?icon=print&label=STAMPA&size={$dimensione_immagini}' alt='Stampa parametri'>
                    <input type='hidden' name='form_stato' value='{$form_stato}'>
                    <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                    <input type='hidden' name='stato_secondario' value='stampa_parametri'>
                    <input type='hidden' name='current_user' value='{$current_user}'>
                    <input type='hidden' name='current_key' value='{$current_key}'>
                    <label><input type="checkbox" name="stampa_descrizioni" value="SI"> descrizioni</label>
                </td>
            </form>
            <form method='post' action='{$SCRIPT_NAME}' target='_blank'>
                <td align='center' colspan='1'>
                    <input type='image' name='bottone' value='Stampa' src='icone/icona.php?icon=print&label=STAMPA&size={$dimensione_immagini}' alt='Stampa dati cancellati'>
                    <input type='hidden' name='form_stato' value='{$form_stato}'>
                    <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                    <input type='hidden' name='stato_secondario' value='stampa_dati_cancellati'>
                    <input type='hidden' name='current_user' value='{$current_user}'>
                    <input type='hidden' name='current_key' value='{$current_key}'>
                </td>
            </form>
            <form method='post' action='traduttore.php' target='_trans'>
                <td align='center' colspan='1'>
                    <input type='image' name='bottone' value='Traduttore' src='icone/multi_lingue{$dimensione_immagini}.gif' alt='Traduzioni'>
                    <input type='hidden' name='form_stato' value='{$form_stato}'>
                    <input type='hidden' name='form_azione' value=''>
                    <input type='hidden' name='current_user' value='{$current_user}'>
                    <input type='hidden' name='current_key' value='{$current_key}'>
                </td>
            </form>
            <form method='post' action='{$SCRIPT_NAME}' target='_blank'>
                <td align='center' colspan='1'>
                    <b>Da</b><br><input type='text' name='data_inizio' size='9'>
                </td>
                <td align='center' colspan='1'>
                    <b>A</b><br><input type='text' name='data_fine' size='9'>
                </td>
                <td align='center' colspan='1'>
                    <b>Filtro</b><br><input type='text' name='testo_libero' size='12'>
                </td>
                <td align='center' colspan='1'>
                    <b>Tipo</b><br>
                    {mastercom_auto_select name='stato_secondario'}
                    stampa_dati_storici###Log storico@@@
                    stampa_dati_gate###Passaggi registrati db gate
                    {/mastercom_auto_select}
                </td>
                <td align='center' colspan='1'>
                    <input type='image' name='bottone' value='Stampa' src='icone/icona.php?icon=print&label=STAMPA&size={$dimensione_immagini}' alt='Stampa log storico'>
                    <input type='hidden' name='form_stato' value='{$form_stato}'>
                    <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                    <input type='hidden' name='current_user' value='{$current_user}'>
                    <input type='hidden' name='current_key' value='{$current_key}'>
                </td>
            </form>
            </tr>
</table>

</td>
</tr>
</table>

</td>
</tr>
</table>

{include file="footer_amministratore.tpl"}
